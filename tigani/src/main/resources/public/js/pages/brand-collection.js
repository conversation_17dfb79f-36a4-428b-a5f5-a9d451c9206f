// Global variables for table management
const BrandCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        // Initialize Preline UI DataTable with JavaScript configuration
        const datatableConfig = {
            paging: true,
            pageLength: 10,
            searching: true,
            ordering: true,
            info: true,
            lengthChange: true,
            scrollCollapse: true,
            ajax: {
                url: appRoutes.get("BE_BRAND_DATA"),
                type: 'GET'
            },
            select: {
                style: 'multi',
                selector: 'td:select-checkbox'
            },
            responsive: {
                details: {
                    type: 'column',
                    target: -1,
                }
            },
            columnDefs: [
                {
                    targets: '_all',
                    className: 'p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200',
                },
                {
                    targets: 0,
                    orderable: false,
                    className: '!py-1 px-5 w-0 select-checkbox',
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            const rowId = row.id || meta.row;
                            return `
                                <div class="flex items-center h-5">
                                    <input id="hs-table-checkbox-${rowId}" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-hs-datatable-row-selecting-individual="">
                                    <label for="hs-table-checkbox-${rowId}" class="sr-only">Checkbox</label>
                                </div>
                            `;
                        }
                        return data;
                    }
                },
                {
                    targets: -1,
                    orderable: false,
                    className: 'w-0 text-center',
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            return _renderActionDropdown(row);
                        }
                        return data;
                    }
                }
            ],
            language: {
                lengthMenu: 'Mostra _MENU_ elementi',
                paginate: {
                    first: 'Primo',
                    last: 'Ultimo',
                    next: 'Successivo',
                    previous: 'Precedente'
                },
                info: 'Mostra da _START_ a _END_ di _TOTAL_ elementi',
                infoEmpty: 'Mostra 0 a 0 di 0 elementi',
                infoFiltered: '(filtrati da _MAX_ elementi totali)',
                emptyTable: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Nessun dato disponibile</p></div></div>',
                zeroRecords: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Nessun dato disponibile</p></div></div>',
                loadingRecords: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Caricamento</p></div></div>',
                processing: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Elaborazione</p></div></div>'
            },
            pagingOptions: {
                pageBtnClasses: 'min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700'
            },
            selecting: true,
            rowSelectingOptions: {
                selectAllSelector: '#hs-table-search-checkbox-all'
            },
            layout: {
                topStart: {
                    buttons: ["copy", "csv", "excel", "pdf", "print"]
                }
            },
            order: [[1, 'asc']]
        };

        const tableEl = document.getElementById('brand-datatable-container');
        const hsDataTable = new HSDataTable(tableEl, datatableConfig);

        // On draw initialize HS components and setup checkbox handlers
        hsDataTable.dataTable.on('draw', function() {
            HSStaticMethods.autoInit();
            _setupCheckboxHandlers();
        });

        window.brandsDataTable = hsDataTable;

        const buttons = document.querySelectorAll('#hs-dropdown-datatable-with-export .hs-dropdown-menu button');
        buttons.forEach((btn) => {
            const type = btn.getAttribute('data-hs-datatable-action-type');

            btn.addEventListener('click', () => hsDataTable.dataTable.button(`.buttons-${type}`).trigger());
        });

        // Initial setup of checkbox handlers
        _setupCheckboxHandlers();
    };

    // Action dropdown renderer
    function _renderActionDropdown(row) {
        // Extract brand ID from the link in the second column (index 1)
        const linkHtml = row[1];
        const brandIdMatch = linkHtml.match(/brandId='([^']+)'/);
        const brandId = brandIdMatch ? brandIdMatch[1] : '';

        // Build action items based on permissions
        let actionItems = '';

        // Archive action - requires edit permission
        if (hasPermission('BRAND_MANAGEMENT', 'edit')) {
            actionItems += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="_archiveSingleRow('${brandId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>
                    Archivia
                </a>`;
        }

        // Delete action - requires delete permission
        if (hasPermission('BRAND_MANAGEMENT', 'delete')) {
            actionItems += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-red-600 hover:bg-red-100 focus:outline-hidden focus:bg-red-100 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30" href="#" onclick="_deleteSingleRow('${brandId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                    Elimina
                </a>`;
        }

        // If no actions are available, don't show the dropdown
        if (!actionItems.trim()) {
            return '<span class="text-sm text-gray-500 dark:text-neutral-400">Nessuna azione disponibile</span>';
        }

        return `
            <div class="hs-dropdown relative inline-flex">
                <button id="hs-table-dropdown-${brandId || Math.random()}" type="button" class="hs-dropdown-toggle py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    Azioni
                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                </button>
                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden divide-y divide-gray-200 min-w-40 z-20 bg-white shadow-2xl rounded-lg p-2 mt-2 dark:divide-neutral-700 dark:bg-neutral-800 dark:border dark:border-neutral-700" role="menu" aria-orientation="vertical">
                    <div class="py-2 first:pt-0 last:pb-0">
                        ${actionItems}
                    </div>
                </div>
            </div>
        `;
    }

    function _componentAddBrand() {
        // Create Brand Button Handler
        const createBrandBtn = document.getElementById('create-brand-btn');
        if (createBrandBtn) {
            createBrandBtn.addEventListener('click', function() {
                try {
                    // Check if required functions are available
                    if (typeof createDynamicOffcanvas !== 'function') {
                        showToast('Errore: funzione offcanvas non disponibile', 'error');
                        return;
                    }

                    if (!appRoutes.has('BE_BRAND_FORM')) {
                        showToast('Errore: route non configurata', 'error');
                        return;
                    }

                    const offcanvas = createDynamicOffcanvas({
                        title: 'Nuovo Marchio',
                        url: appRoutes.get('BE_BRAND_FORM'),
                        entity: 'brand',
                        onContentLoaded: function(offcanvasElement, contentContainer) {
                            try {
                                // Initialize brand form components after content is loaded
                                if (typeof BrandForm !== 'undefined' && BrandForm.init) {
                                    BrandForm.init();
                                }
                            } catch (initError) {
                                console.error('Error initializing form:', initError);
                                showToast('Errore nell\'inizializzazione del modulo', 'error');
                            }
                        }
                    });
                } catch (error) {
                    console.error('Error creating offcanvas:', error);
                    showToast('Errore nell\'apertura del modulo', 'error');
                }
            });
        } else {
            console.warn('Create brand button not found');
        }
    }

    function _componentEditBrand() {
        // Edit Brand Click Handler for table rows
        $(document).on('click', 'a[brandId]', function(e) {
            e.preventDefault();

            try {
                const brandId = $(this).attr('brandId');
                const brandName = $(this).text().trim();

                if (!brandId) {
                    showToast('Errore: ID marchio non trovato', 'error');
                    return;
                }

                // Check if required functions are available
                if (typeof createDynamicOffcanvas !== 'function') {
                    showToast('Errore: funzione offcanvas non disponibile', 'error');
                    return;
                }

                if (!appRoutes.has('BE_BRAND_FORM')) {
                    showToast('Errore: route non configurata', 'error');
                    return;
                }

                const offcanvas = createDynamicOffcanvas({
                    title: 'Modifica Marchio: ' + (brandName || 'Sconosciuto'),
                    url: appRoutes.get('BE_BRAND_FORM') + '?brandId=' + encodeURIComponent(brandId),
                    entity: 'brand',
                    entityId: brandId,
                    onContentLoaded: function(offcanvasElement, contentContainer) {
                        try {
                            // Initialize brand form components after content is loaded
                            if (typeof BrandForm !== 'undefined' && BrandForm.init) {
                                BrandForm.init();
                            }
                        } catch (initError) {
                            console.error('Error initializing form:', initError);
                            showToast('Errore nell\'inizializzazione del modulo', 'error');
                        }
                    }
                });
            } catch (error) {
                console.error('Error opening edit form:', error);
                showToast('Errore nell\'apertura del modulo di modifica', 'error');
            }
        });
    }

    // Setup manual checkbox handlers for Preline UI compatibility
    function _setupCheckboxHandlers() {
        // Handle select-all checkbox
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.removeEventListener('change', _handleSelectAll); // Remove existing listener
            selectAllCheckbox.addEventListener('change', _handleSelectAll);
        }

        // Handle individual row checkboxes
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        individualCheckboxes.forEach(checkbox => {
            checkbox.removeEventListener('change', _handleIndividualSelect); // Remove existing listener
            checkbox.addEventListener('change', _handleIndividualSelect);
        });

        // Update bulk action buttons state
        _updateBulkActionButtons();
    }

    function _handleSelectAll(event) {
        const isChecked = event.target.checked;
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');

        individualCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });

        _updateBulkActionButtons();
    }

    function _handleIndividualSelect() {
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        const checkedBoxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

        if (selectAllCheckbox) {
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedBoxes.length === individualCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        _updateBulkActionButtons();
    }

    function _reloadTable(archived) {
        var newLink = appRoutes.get("BE_BRAND_DATA");
        var params = [];

        // check archiviati
        var isArchivedChecked = archived || $("#brand_archived:checked").length > 0;
        if (isArchivedChecked) {
            params.push("archived=" + isArchivedChecked);
        }

        // Build final URL
        if (params.length > 0) {
            newLink += "?" + params.join("&");
        }

        if (window.brandsDataTable && window.brandsDataTable.dataTable) {
            window.brandsDataTable.dataTable.ajax.url(newLink).load();
        }
    }

    function _archiveSelectedRows() {
        // Check permission first
        if (!hasPermission('BRAND_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno un elemento.', 'warning');
            return;
        }

        const brandIds = selectedRows.map(row => row.id).join(',');
        const formData = new FormData();
        formData.append('brandIds', brandIds);
        formData.append('operation', "archive");
        formData.append('fromArchived', $("#brand_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_BRAND_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Dati salvati correttamente.', 'success');
                _clearSelection();
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during brand archive', error);
            }
        });
    }

    function _deleteSelectedRows() {
        // Check permission first
        if (!hasPermission('BRAND_MANAGEMENT', 'delete')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno un elemento.', 'warning');
            return;
        }

        $.confirm({
            title: 'Conferma eliminazione',
            content: `Sei sicuro di voler eliminare ${selectedRows.length} element${selectedRows.length > 1 ? 'i' : 'o'}? Questa azione non può essere annullata.`,
            type: 'red',
            typeAnimated: true,
            buttons: {
                elimina: {
                    text: 'Elimina',
                    btnClass: 'btn-red',
                    action: function () {
                        _performDeleteSelectedRows(selectedRows);
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _performDeleteSelectedRows(selectedRows) {

        const brandIds = selectedRows.map(row => row.id).join(',');
        const formData = new FormData();
        formData.append('brandIds', brandIds);
        formData.append('operation', "delete");
        formData.append('fromArchived', $("#brand_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_BRAND_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Dati eliminati correttamente.', 'success');
                _clearSelection();
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'eliminazione', 'error');
                console.error('Error during brand delete', error);
            }
        });
    }

    // Helper functions for selection management
    function _getSelectedRows() {
        const selectedRows = [];
        const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row && window.brandsDataTable && window.brandsDataTable.dataTable) {
                const rowData = window.brandsDataTable.dataTable.row(row).data();
                if (rowData && rowData.length > 1) {
                    // Extract brand ID from the link in the second column (index 1)
                    const linkHtml = rowData[1];
                    const brandIdMatch = linkHtml.match(/brandId='([^']+)'/);
                    if (brandIdMatch && brandIdMatch[1]) {
                        selectedRows.push({ id: brandIdMatch[1], data: rowData });
                    }
                }
            }
        });

        return selectedRows;
    }

    function _clearSelection() {
        const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual], #hs-table-search-checkbox-all');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.indeterminate = false;
        });
        _updateBulkActionButtons();
    }

    function _updateBulkActionButtons() {
        const selectedCount = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked').length;
        const bulkActionContainer = document.getElementById('bulk-actions-container');
        const selectedCountElement = document.getElementById('selected-count');

        if (bulkActionContainer && selectedCountElement) {
            if (selectedCount > 0) {
                // Show the enhanced bulk actions container
                bulkActionContainer.classList.remove('hidden');
                selectedCountElement.textContent = selectedCount;
            } else {
                // Hide the enhanced bulk actions container
                bulkActionContainer.classList.add('hidden');
            }
        }

        // Legacy support for old bulk action buttons (if any still exist)
        const bulkActionButtons = document.querySelectorAll('.bulk-action-btn');
        bulkActionButtons.forEach(button => {
            // Update button text with count
            const buttonTextSpan = button.querySelector('.bulk-action-text');
            if (buttonTextSpan) {
                if (selectedCount > 0) {
                    buttonTextSpan.textContent = `Azioni (${selectedCount})`;
                } else {
                    buttonTextSpan.textContent = 'Azioni';
                }
            }

            if (selectedCount > 0) {
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
            }
        });
    }

    // Individual row action functions
    function _archiveSingleRow(brandId) {
        // Check permission first
        if (!hasPermission('BRAND_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }
        _performSingleRowAction(brandId, 'archive');
    }

    function _deleteSingleRow(brandId) {
        // Check permission first
        if (!hasPermission('BRAND_MANAGEMENT', 'delete')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        $.confirm({
            title: 'Conferma eliminazione',
            content: 'Sei sicuro di voler eliminare questo marchio? Questa azione non può essere annullata.',
            type: 'red',
            typeAnimated: true,
            buttons: {
                elimina: {
                    text: 'Elimina',
                    btnClass: 'btn-red',
                    action: function () {
                        _performSingleRowAction(brandId, 'delete');
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _performSingleRowAction(brandId, operation) {
        const formData = new FormData();
        formData.append('brandIds', brandId);
        formData.append('operation', operation);
        formData.append('fromArchived', $("#brand_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_BRAND_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Operazione completata correttamente.', 'success');
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during brand operation', error);
            }
        });
    }

    // Make individual action functions globally accessible
    window._archiveSingleRow = _archiveSingleRow;
    window._deleteSingleRow = _deleteSingleRow;

    //
    // Return objects assigned to module
    //

    return {
        init: function () {
            _componentDatatable();
            _componentAddBrand();
            _componentEditBrand();
        },
        reloadTable: _reloadTable,
        archiveSelectedRows: _archiveSelectedRows,
        deleteSelectedRows: _deleteSelectedRows,
        getSelectedRows: _getSelectedRows,
        clearSelection: _clearSelection
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    BrandCollection.init();
});
